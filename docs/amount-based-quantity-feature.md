# Amount-Based Quantity Feature

## Overview
This feature allows shopkeepers to easily adjust product quantities in orders by specifying a target selling amount instead of manually calculating quantities. This is particularly useful when customers want to buy a specific amount worth of products (e.g., "30 taka worth of potatoes").

## How It Works

### For Shopkeepers
1. **Access the Feature**: In the order details page, click on the total amount of any order item
2. **Enter Target Amount**: A simple modal will open where you can enter the desired selling amount
3. **Preview Calculation**: See the calculated quantity in real-time
4. **Update**: Click "Update" to apply the changes

### Example Scenario
- Customer wants 30 taka worth of potatoes
- Potato price is 23 taka per kg
- Shopkeeper clicks on the total amount for potatoes in the order
- Enters "30" in the target amount field
- System calculates: 30 ÷ 23 = 1.30 kg
- Quantity is automatically updated to 1.30 kg

## Technical Implementation

### New Server Action
- `updateOrderItemQuantityByAmount`: Calculates and updates quantity based on target amount
- Formula: `new_quantity = target_amount / unit_price`
- Automatically recalculates order subtotal and profit

### UI Components
- **AmountBasedQuantityModal**: Simple, clean modal dialog for entering target amount
- **Real-time Calculation**: Shows the calculated quantity as you type

### Features
- **Clickable Total Amounts**: Both desktop table and mobile card views support clicking on total amounts
- **Simple Interface**: Clean, minimal design with only essential information
- **Real-time Calculation**: Shows calculated quantity as you type
- **Input Validation**: Ensures positive amounts and proper formatting
- **Responsive Design**: Works on both desktop and mobile devices
- **Bilingual Support**: Supports both Bengali and English

## Benefits
1. **Faster Checkout**: No need to manually calculate quantities
2. **Reduced Errors**: Automatic calculation eliminates human error
3. **Better Customer Service**: Quickly accommodate customer requests for specific amounts
4. **Improved Efficiency**: Streamlines the order adjustment process

## Usage Tips
- Simply type the target amount (e.g., 30 for 30 taka)
- The feature works with decimal quantities (e.g., 1.30 kg)
- Changes are immediately reflected in order totals and profit calculations
- The feature is available for both admin and super admin users

## Accessibility
- Tooltips explain the feature when hovering over total amounts
- Clear visual feedback with hover effects
- Keyboard navigation support
- Screen reader friendly labels and descriptions
