"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@udoy/components/ui/form";
import { withError } from "@udoy/utils/app-error";
import { updateOrderItemQuantityByAmount } from "../actions";
import Locale from "@udoy/components/Locale/Client";

// Form validation schema
const amountFormSchema = z.object({
  targetAmount: z
    .number()
    .min(0.01, "Amount must be greater than 0")
    .max(100000, "Amount is too large"),
});

type AmountFormValues = z.infer<typeof amountFormSchema>;

interface AmountBasedQuantityModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderItem: {
    id: number;
    price: number;
    quantity: number;
    product: {
      name: string;
      nam?: string;
    };
  };
  orderId: number;
  onQuantityUpdated?: () => void;
}

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("bn-BD", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 0,
  }).format(amount);
}

export function AmountBasedQuantityModal({
  isOpen,
  onClose,
  orderItem,
  orderId,
  onQuantityUpdated,
}: AmountBasedQuantityModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AmountFormValues>({
    resolver: zodResolver(amountFormSchema),
    defaultValues: {
      targetAmount: orderItem.price * orderItem.quantity,
    },
  });

  const targetAmount = form.watch("targetAmount");
  const calculatedQuantity = targetAmount > 0 ? targetAmount / orderItem.price : 0;

  // Handle amount-based quantity update submission
  async function handleUpdateQuantity(values: AmountFormValues) {
    setIsLoading(true);
    try {
      const result = await withError(
        updateOrderItemQuantityByAmount({
          itemId: orderItem.id,
          orderId: orderId,
          targetAmount: values.targetAmount,
        })
      );
      if (result && typeof result === 'object' && 'success' in result) {
        toast.success(
          `Quantity updated to ${result.newQuantity} for ${formatCurrency(values.targetAmount)}`
        );
        onQuantityUpdated?.();
        onClose();
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to update quantity");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            <Locale bn="পরিমাণ নির্ধারণ করুন">Set Quantity by Amount</Locale>
          </DialogTitle>
          <DialogDescription>
            <Locale bn="বিক্রয় মূল্য দিয়ে পরিমাণ নির্ধারণ করুন">
              Set the quantity by entering the target selling amount
            </Locale>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Product Info */}
          <div className="bg-muted/50 p-3 rounded-md">
            <div className="font-medium text-sm">
              <Locale bn={orderItem.product.nam}>
                {orderItem.product.name}
              </Locale>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              <Locale bn="একক দাম">Unit Price</Locale>: {formatCurrency(orderItem.price)}
            </div>
            <div className="text-xs text-muted-foreground">
              <Locale bn="বর্তমান পরিমাণ">Current Quantity</Locale>: {orderItem.quantity}
            </div>
            <div className="text-xs text-muted-foreground">
              <Locale bn="বর্তমান মোট">Current Total</Locale>: {formatCurrency(orderItem.price * orderItem.quantity)}
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleUpdateQuantity)} className="space-y-4">
              <FormField
                control={form.control}
                name="targetAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Locale bn="লক্ষ্য বিক্রয় মূল্য (৳)">Target Selling Amount (৳)</Locale>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder="30"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        disabled={isLoading}
                      />
                    </FormControl>

                    {/* Quick amount buttons */}
                    <div className="flex gap-2 mt-2">
                      <div className="text-xs text-muted-foreground mb-1 w-full">
                        <Locale bn="দ্রুত নির্বাচন">Quick amounts</Locale>:
                      </div>
                    </div>
                    <div className="flex gap-2 flex-wrap">
                      {[20, 30, 50, 100, 200, 500].map((amount) => (
                        <Button
                          key={amount}
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-8 px-3 text-xs"
                          onClick={() => field.onChange(amount)}
                          disabled={isLoading}
                        >
                          ৳{amount}
                        </Button>
                      ))}
                    </div>

                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Calculation Preview */}
              {targetAmount > 0 && (
                <div className="bg-blue-50 border border-blue-200 p-3 rounded-md">
                  <div className="text-sm font-medium text-blue-900 mb-2">
                    <Locale bn="গণনার পূর্বরূপ">Calculation Preview</Locale>
                  </div>
                  <div className="space-y-1 text-xs text-blue-800">
                    <div className="flex justify-between">
                      <span><Locale bn="লক্ষ্য মূল্য">Target Amount</Locale>:</span>
                      <span>{formatCurrency(targetAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span><Locale bn="একক দাম">Unit Price</Locale>:</span>
                      <span>{formatCurrency(orderItem.price)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span><Locale bn="নতুন পরিমাণ">New Quantity</Locale>:</span>
                      <span>{calculatedQuantity.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}

              <DialogFooter className="gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  <Locale bn="বাতিল">Cancel</Locale>
                </Button>
                <Button type="submit" disabled={isLoading || targetAmount <= 0}>
                  {isLoading ? (
                    <Locale bn="আপডেট হচ্ছে...">Updating...</Locale>
                  ) : (
                    <Locale bn="পরিমাণ আপডেট করুন">Update Quantity</Locale>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
